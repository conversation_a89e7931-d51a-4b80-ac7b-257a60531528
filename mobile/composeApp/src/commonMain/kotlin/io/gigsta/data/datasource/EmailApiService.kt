package io.gigsta.data.datasource

import io.gigsta.data.model.EmailApplicationResponse
import io.gigsta.data.network.NetworkClient
import io.gigsta.data.network.NetworkConfig
import io.gigsta.domain.model.ResumeInfo
import io.gigsta.domain.model.JobInfo
import io.ktor.client.call.*
import io.ktor.client.request.forms.*
import io.ktor.client.request.post
import io.ktor.client.request.setBody
import io.ktor.http.*

class EmailApiService {

    private val httpClient = NetworkClient.client

    suspend fun generateEmailApplication(
        resumeInfo: ResumeInfo,
        jobInfo: JobInfo
    ): Result<EmailApplicationResponse> {
        return try {
            val formDataList = formData {
                // Add job description if provided
                jobInfo.description?.let { description ->
                    append("\"jobDescription\"", "\"${description}\"")
                }

                // Add job image if provided
                if (jobInfo.imageData != null && jobInfo.imageMimeType != null && jobInfo.imageFileName != null) {
                    append("jobImage", jobInfo.imageData, Headers.build {
                        append(HttpHeaders.ContentType, jobInfo.imageMimeType)
                        append(HttpHeaders.ContentDisposition, "form-data; name=\"image\"; filename=\"${jobInfo.imageFileName}\"")
                    })
                }
                jobInfo.imageData?.let { imageData ->

                }

                // For unauthenticated resume (when fileData is available)
                resumeInfo.fileData?.let { fileData ->
                    // append("unauthenticatedResumeFile", fileData)
                    // append("unauthenticatedResumeFileName", resumeInfo.fileName)
                }
            }

            val response = httpClient.submitFormWithBinaryData(
                url = "${NetworkConfig.API_BASE_URL}/api/generate-email-application",
                formData = formDataList
            )
//            val response = httpClient.post(
//                url = Url("${NetworkConfig.API_BASE_URL}/api/generate-email-application")
//            ) {
//                setBody(MultiPartFormDataContent(formDataList))
//            }
//            val response = httpClient.submitForm(
//                url = "${NetworkConfig.API_BASE_URL}/api/generate-email-application",
//                formParameters = parameters {
//                    append("jobDescription", jobInfo.description.orEmpty())
//                }
//            )

            if (response.status.isSuccess()) {
                val emailResponse = response.body<EmailApplicationResponse>()
                Result.success(emailResponse)
            } else {
                val errorBody = response.body<String>()
                Result.failure(Exception("API Error: ${response.status.value} - $errorBody"))
            }

        } catch (e: Exception) {
            println("Error generating email application: ${e.message}")
            Result.failure(Exception("Failed to generate email application: ${e.message}"))
        }
    }

    private fun getMimeTypeFromFileName(fileName: String?): String {
        return when (fileName?.lowercase()?.substringAfterLast('.')) {
            "pdf" -> "application/pdf"
            "docx" -> "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
            "doc" -> "application/msword"
            "txt" -> "text/plain"
            "png" -> "image/png"
            "jpg", "jpeg" -> "image/jpeg"
            else -> "application/octet-stream"
        }
    }
}
